"""Base model class for data validation and serialization."""

from datetime import datetime
from typing import Any, Dict, Optional, Union
from bson import ObjectId


class BaseModel:
    """Base model class with common functionality."""
    
    def __init__(self, **kwargs):
        """Initialize model with data."""
        self._data = {}
        self.update(**kwargs)
    
    def update(self, **kwargs) -> None:
        """Update model data."""
        for key, value in kwargs.items():
            self._data[key] = value
    
    def to_dict(self, include_id: bool = True) -> Dict[str, Any]:
        """
        Convert model to dictionary.
        
        Args:
            include_id: Whether to include _id field
            
        Returns:
            Dictionary representation
        """
        data = self._data.copy()
        
        if not include_id and '_id' in data:
            del data['_id']
        
        # Convert ObjectId to string for JSON serialization
        if '_id' in data and isinstance(data['_id'], ObjectId):
            data['_id'] = str(data['_id'])
        
        return data
    
    def to_mongo_dict(self) -> Dict[str, Any]:
        """
        Convert model to MongoDB-compatible dictionary.
        
        Returns:
            Dictionary ready for MongoDB operations
        """
        return self._data.copy()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BaseModel':
        """
        Create model instance from dictionary.
        
        Args:
            data: Dictionary data
            
        Returns:
            Model instance
        """
        return cls(**data)
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get value by key."""
        return self._data.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set value by key."""
        self._data[key] = value
    
    @property
    def id(self) -> Optional[Union[str, ObjectId]]:
        """Get document ID."""
        return self._data.get('_id')
    
    @property
    def created_at(self) -> Optional[datetime]:
        """Get creation timestamp."""
        return self._data.get('created_at')
    
    @property
    def updated_at(self) -> Optional[datetime]:
        """Get update timestamp."""
        return self._data.get('updated_at')
    
    def __getitem__(self, key: str) -> Any:
        """Get item by key."""
        return self._data[key]
    
    def __setitem__(self, key: str, value: Any) -> None:
        """Set item by key."""
        self._data[key] = value
    
    def __contains__(self, key: str) -> bool:
        """Check if key exists."""
        return key in self._data
    
    def __repr__(self) -> str:
        """String representation."""
        return f"{self.__class__.__name__}({self._data})"
