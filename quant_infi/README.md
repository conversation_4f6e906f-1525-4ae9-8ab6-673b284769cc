# Quant Infi - MongoDB DAL Layer

A simple and efficient MongoDB Data Access Layer (DAL) for quantitative trading applications.

## Features

- **Simple Configuration**: Environment variable-based configuration using `.env` files
- **Connection Management**: Automatic connection pooling and management
- **Base DAL Class**: Common CRUD operations with error handling
- **Model Support**: Base model class for data validation and serialization
- **Timezone Support**: Configured for Asia/Shanghai timezone
- **Logging**: Comprehensive logging for debugging and monitoring

## Project Structure

```
quant_infi/
├── .env                    # Environment variables
├── config.py              # Configuration management
├── database/              # Database layer
│   ├── __init__.py
│   ├── connection.py      # MongoDB connection management
│   └── base_dal.py        # Base DAL with CRUD operations
├── models/                # Data models
│   ├── __init__.py
│   └── base_model.py      # Base model class
├── main.py                # Main application with demo
├── example_usage.py       # Usage examples
└── README.md              # This file
```

## Configuration

The application uses environment variables defined in `.env`:

```env
# MongoDB Configuration
MONGODB_HOST=127.0.0.1
MONGODB_PORT=27017
MONGODB_USERNAME=admin
MONGODB_PASSWORD=admin
MONGODB_AUTH_SOURCE=admin
MONGODB_DATABASE=future_data
MONGODB_TIMEZONE=Asia/Shanghai

# Connection Pool Settings
MONGODB_MAX_POOL_SIZE=100
MONGODB_MIN_POOL_SIZE=10
MONGODB_MAX_IDLE_TIME_MS=30000
MONGODB_CONNECT_TIMEOUT_MS=5000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000
```

## Quick Start

1. **Install dependencies** (already included in pyproject.toml):
   ```bash
   uv sync
   ```

2. **Configure MongoDB** by editing `.env` file with your MongoDB settings

3. **Run the demo**:
   ```bash
   uv run python main.py
   ```

## Usage Examples

### Basic DAL Usage

```python
from database import BaseDAL
from models import BaseModel

# Create a custom DAL
class FutureDataDAL(BaseDAL):
    def __init__(self):
        super().__init__('future_data')

    def find_by_symbol(self, symbol: str):
        return self.find_many({'symbol': symbol})

# Use the DAL
dal = FutureDataDAL()

# Insert data
result = dal.insert_one({
    'symbol': 'RB2501',
    'price': 3850.0,
    'volume': 1000
})

# Find data
data = dal.find_by_symbol('RB2501')

# Update data
dal.update_one(
    {'symbol': 'RB2501'},
    {'$set': {'price': 3860.0}}
)

# Count documents
count = dal.count_documents({'symbol': 'RB2501'})
```

### Using Models

```python
from models import BaseModel

class FutureDataModel(BaseModel):
    def __init__(self, symbol=None, price=None, **kwargs):
        super().__init__(**kwargs)
        if symbol:
            self.set('symbol', symbol)
        if price:
            self.set('price', price)

# Create model instance
data = FutureDataModel(symbol='RB2501', price=3850.0)

# Convert to MongoDB document
mongo_doc = data.to_mongo_dict()

# Insert using DAL
dal.insert_one(mongo_doc)
```

## Available DAL Methods

### Create Operations
- `insert_one(document, add_timestamps=True)` - Insert single document
- `insert_many(documents, add_timestamps=True)` - Insert multiple documents

### Read Operations
- `find_one(filter_dict, projection)` - Find single document
- `find_many(filter_dict, projection, sort, limit, skip)` - Find multiple documents
- `find_by_id(object_id)` - Find by ObjectId
- `count_documents(filter_dict)` - Count matching documents

### Update Operations
- `update_one(filter_dict, update_dict, upsert, add_timestamp)` - Update single document
- `update_many(filter_dict, update_dict, add_timestamp)` - Update multiple documents

### Delete Operations
- `delete_one(filter_dict)` - Delete single document
- `delete_many(filter_dict)` - Delete multiple documents

### Index Operations
- `create_index(keys, **kwargs)` - Create database index

## Model Features

### Base Model Methods
- `to_dict(include_id=True)` - Convert to dictionary
- `to_mongo_dict()` - Convert to MongoDB-compatible dictionary
- `from_dict(data)` - Create instance from dictionary
- `get(key, default)` - Get value by key
- `set(key, value)` - Set value by key

### Properties
- `id` - Document ObjectId
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

## Error Handling

The DAL includes comprehensive error handling for:
- Connection failures
- Duplicate key errors
- General MongoDB errors
- Timeout errors

All errors are logged with appropriate detail levels.

## Logging

The application uses Python's built-in logging module. Configure logging level in your application:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## Dependencies

- `pymongo` - MongoDB driver
- `python-dotenv` - Environment variable management
- Standard library modules for logging, datetime, etc.

## License

This project is part of the quant-infi package.