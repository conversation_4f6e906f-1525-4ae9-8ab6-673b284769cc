"""Example usage of the MongoDB DAL layer."""

import logging
from datetime import datetime, timedelta
from database import BaseDAL
from models import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TradeDAL(BaseDAL):
    """DAL for trade data."""
    
    def __init__(self):
        super().__init__('trades')
    
    def find_profitable_trades(self):
        """Find trades with profit > 0."""
        return self.find_many({'profit': {'$gt': 0}})
    
    def get_trades_by_strategy(self, strategy_name: str):
        """Get trades by strategy name."""
        return self.find_many({'strategy': strategy_name})


class Trade(BaseModel):
    """Trade model."""
    
    def __init__(self, symbol: str = None, entry_price: float = None, 
                 exit_price: float = None, quantity: int = None, 
                 strategy: str = None, **kwargs):
        super().__init__(**kwargs)
        if symbol:
            self.set('symbol', symbol)
        if entry_price:
            self.set('entry_price', entry_price)
        if exit_price:
            self.set('exit_price', exit_price)
        if quantity:
            self.set('quantity', quantity)
        if strategy:
            self.set('strategy', strategy)
        
        # Calculate profit if both prices are available
        if entry_price and exit_price and quantity:
            profit = (exit_price - entry_price) * quantity
            self.set('profit', profit)


def example_usage():
    """Example of how to use the DAL."""
    
    # Initialize DAL
    trade_dal = TradeDAL()
    
    # Create sample trades
    trades = [
        Trade(
            symbol='RB2501',
            entry_price=3850.0,
            exit_price=3860.0,
            quantity=10,
            strategy='momentum',
        ),
        Trade(
            symbol='I2501',
            entry_price=580.0,
            exit_price=575.0,
            quantity=5,
            strategy='mean_reversion',
        ),
        Trade(
            symbol='RB2501',
            entry_price=3840.0,
            exit_price=3855.0,
            quantity=8,
            strategy='breakout',
        )
    ]
    
    # Insert trades
    trade_docs = [trade.to_mongo_dict() for trade in trades]
    result = trade_dal.insert_many(trade_docs)
    logger.info(f"Inserted {len(result.inserted_ids)} trades")
    
    # Find profitable trades
    profitable = trade_dal.find_profitable_trades()
    logger.info(f"Found {len(profitable)} profitable trades")
    
    # Find trades by strategy
    momentum_trades = trade_dal.get_trades_by_strategy('momentum')
    logger.info(f"Found {len(momentum_trades)} momentum trades")
    
    # Update a trade
    trade_dal.update_one(
        {'symbol': 'RB2501', 'strategy': 'momentum'},
        {'$set': {'notes': 'Strong momentum signal'}}
    )
    
    # Count total trades
    total_trades = trade_dal.count_documents()
    logger.info(f"Total trades in database: {total_trades}")
    
    # Create indexes for better performance
    trade_dal.create_index([('symbol', 1)])
    trade_dal.create_index([('strategy', 1)])
    trade_dal.create_index([('created_at', -1)])
    
    logger.info("Example usage completed")


if __name__ == "__main__":
    example_usage()
