"""Test MongoDB connection and basic operations."""

import logging
from database import get_database, BaseDAL
from models import BaseModel
from config import mongodb_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_connection():
    """Test MongoDB connection."""
    try:
        db = get_database()
        # Test connection with a simple ping
        db.command('ping')
        logger.info(f"✅ Successfully connected to MongoDB: {mongodb_config.database}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to connect to MongoDB: {e}")
        return False


def test_basic_operations():
    """Test basic DAL operations."""
    try:
        # Create a test DAL
        class TestDAL(BaseDAL):
            def __init__(self):
                super().__init__('test_collection')
        
        dal = TestDAL()
        
        # Test insert
        test_doc = {
            'name': 'test_document',
            'value': 123,
            'description': 'This is a test document'
        }
        
        result = dal.insert_one(test_doc)
        logger.info(f"✅ Insert test passed. Document ID: {result.inserted_id}")
        
        # Test find
        found_doc = dal.find_one({'name': 'test_document'})
        if found_doc:
            logger.info(f"✅ Find test passed. Found document: {found_doc['name']}")
        else:
            logger.error("❌ Find test failed")
            return False
        
        # Test update
        update_result = dal.update_one(
            {'name': 'test_document'},
            {'$set': {'value': 456}}
        )
        logger.info(f"✅ Update test passed. Modified count: {update_result.modified_count}")
        
        # Test count
        count = dal.count_documents({'name': 'test_document'})
        logger.info(f"✅ Count test passed. Document count: {count}")
        
        # Test delete (cleanup)
        delete_result = dal.delete_many({'name': 'test_document'})
        logger.info(f"✅ Delete test passed. Deleted count: {delete_result.deleted_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Basic operations test failed: {e}")
        return False


def test_model():
    """Test model functionality."""
    try:
        # Create a model
        model = BaseModel(
            symbol='TEST001',
            price=100.50,
            volume=1000
        )
        
        # Test model methods
        model_dict = model.to_dict()
        logger.info(f"✅ Model to_dict test passed: {model_dict}")
        
        # Test model from dict
        new_model = BaseModel.from_dict(model_dict)
        logger.info(f"✅ Model from_dict test passed: {new_model.get('symbol')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Model test failed: {e}")
        return False


def main():
    """Run all tests."""
    logger.info("🚀 Starting MongoDB DAL tests...")
    logger.info(f"Configuration: {mongodb_config}")
    
    tests = [
        ("Connection Test", test_connection),
        ("Model Test", test_model),
        ("Basic Operations Test", test_basic_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
    
    logger.info(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! MongoDB DAL is ready to use.")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check your MongoDB configuration.")
        return 1


if __name__ == "__main__":
    exit(main())
