"""Main application demonstrating MongoDB DAL usage."""

import logging
from datetime import datetime
from database import BaseDAL, get_database
from models import BaseModel
from config import mongodb_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FutureDataDAL(BaseDAL):
    """DAL for future trading data."""

    def __init__(self):
        super().__init__('future_data')

    def find_by_symbol(self, symbol: str):
        """Find data by trading symbol."""
        return self.find_many({'symbol': symbol})

    def find_by_date_range(self, start_date: datetime, end_date: datetime):
        """Find data within date range."""
        return self.find_many({
            'timestamp': {
                '$gte': start_date,
                '$lte': end_date
            }
        })


class FutureDataModel(BaseModel):
    """Model for future trading data."""

    def __init__(self, symbol: str = None, price: float = None,
                 volume: int = None, timestamp: datetime = None, **kwargs):
        super().__init__(**kwargs)
        if symbol:
            self.set('symbol', symbol)
        if price:
            self.set('price', price)
        if volume:
            self.set('volume', volume)
        if timestamp:
            self.set('timestamp', timestamp)

    @property
    def symbol(self) -> str:
        return self.get('symbol')

    @property
    def price(self) -> float:
        return self.get('price')

    @property
    def volume(self) -> int:
        return self.get('volume')

    @property
    def timestamp(self) -> datetime:
        return self.get('timestamp')


def demo_dal_operations():
    """Demonstrate DAL operations."""
    logger.info("Starting MongoDB DAL demonstration")

    try:
        # Initialize DAL
        future_dal = FutureDataDAL()

        # Test connection
        db = get_database()
        logger.info(f"Connected to database: {db.name}")

        # Create sample data
        sample_data = FutureDataModel(
            symbol='RB2501',
            price=3850.0,
            volume=1000,
            timestamp=datetime.now()
        )

        # Insert data
        result = future_dal.insert_one(sample_data.to_mongo_dict())
        logger.info(f"Inserted document with ID: {result.inserted_id}")

        # Find data
        found_data = future_dal.find_by_symbol('RB2501')
        logger.info(f"Found {len(found_data)} documents for symbol RB2501")

        # Count documents
        count = future_dal.count_documents()
        logger.info(f"Total documents in collection: {count}")

        # Update data
        update_result = future_dal.update_one(
            {'symbol': 'RB2501'},
            {'$set': {'price': 3860.0}}
        )
        logger.info(f"Updated {update_result.modified_count} document(s)")

        # Create index for better performance
        index_name = future_dal.create_index([('symbol', 1), ('timestamp', -1)])
        logger.info(f"Created index: {index_name}")

        logger.info("DAL demonstration completed successfully")

    except Exception as e:
        logger.error(f"Error during DAL demonstration: {e}")
        raise


def main():
    """Main application entry point."""
    print(f"MongoDB DAL Demo - Connected to: {mongodb_config}")
    print(f"Timezone: {mongodb_config.timezone}")

    try:
        demo_dal_operations()
    except Exception as e:
        logger.error(f"Application error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
