"""Configuration management for the application."""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path(__file__).parent / '.env'
load_dotenv(dotenv_path=env_path)


class MongoDBConfig:
    """MongoDB configuration settings."""
    
    def __init__(self):
        self.host = os.getenv('MONGODB_HOST', '127.0.0.1')
        self.port = int(os.getenv('MONGODB_PORT', 27017))
        self.username = os.getenv('MONGODB_USERNAME', 'admin')
        self.password = os.getenv('MONGODB_PASSWORD', 'admin')
        self.auth_source = os.getenv('MONGODB_AUTH_SOURCE', 'admin')
        self.database = os.getenv('MONGODB_DATABASE', 'future_data')
        self.timezone = os.getenv('MONGODB_TIMEZONE', 'Asia/Shanghai')
        
        # Connection pool settings
        self.max_pool_size = int(os.getenv('MONGODB_MAX_POOL_SIZE', 100))
        self.min_pool_size = int(os.getenv('MONGODB_MIN_POOL_SIZE', 10))
        self.max_idle_time_ms = int(os.getenv('MONGODB_MAX_IDLE_TIME_MS', 30000))
        self.connect_timeout_ms = int(os.getenv('MONGODB_CONNECT_TIMEOUT_MS', 5000))
        self.server_selection_timeout_ms = int(os.getenv('MONGODB_SERVER_SELECTION_TIMEOUT_MS', 5000))
    
    @property
    def connection_string(self) -> str:
        """Generate MongoDB connection string."""
        if self.username and self.password:
            return (f"mongodb://{self.username}:{self.password}@{self.host}:{self.port}/"
                   f"{self.database}?authSource={self.auth_source}")
        else:
            return f"mongodb://{self.host}:{self.port}/{self.database}"
    
    def __repr__(self):
        return f"MongoDBConfig(host={self.host}, port={self.port}, database={self.database})"


# Global configuration instance
mongodb_config = MongoDBConfig()
