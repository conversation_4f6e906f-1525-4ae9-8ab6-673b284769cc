"""Base Data Access Layer for MongoDB operations."""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from pymongo.collection import Collection
from pymongo.errors import DuplicateKeyError, PyMongoError
from pymongo.results import InsertOneResult, InsertManyResult, UpdateResult, DeleteResult
from bson import ObjectId

from .connection import get_database
from config import mongodb_config

logger = logging.getLogger(__name__)


class BaseDAL:
    """Base Data Access Layer with common CRUD operations."""
    
    def __init__(self, collection_name: str):
        """
        Initialize DAL with collection name.
        
        Args:
            collection_name: Name of the MongoDB collection
        """
        self.collection_name = collection_name
        self._collection: Optional[Collection] = None
    
    @property
    def collection(self) -> Collection:
        """Get MongoDB collection."""
        if self._collection is None:
            database = get_database()
            self._collection = database[self.collection_name]
        return self._collection
    
    def insert_one(self, document: Dict[str, Any], add_timestamps: bool = True) -> InsertOneResult:
        """
        Insert a single document.
        
        Args:
            document: Document to insert
            add_timestamps: Whether to add created_at and updated_at timestamps
            
        Returns:
            InsertOneResult object
        """
        try:
            if add_timestamps:
                now = datetime.now()
                document['created_at'] = now
                document['updated_at'] = now
            
            result = self.collection.insert_one(document)
            logger.debug(f"Inserted document with ID: {result.inserted_id}")
            return result
            
        except DuplicateKeyError as e:
            logger.error(f"Duplicate key error in {self.collection_name}: {e}")
            raise
        except PyMongoError as e:
            logger.error(f"MongoDB error in insert_one for {self.collection_name}: {e}")
            raise
    
    def insert_many(self, documents: List[Dict[str, Any]], add_timestamps: bool = True) -> InsertManyResult:
        """
        Insert multiple documents.
        
        Args:
            documents: List of documents to insert
            add_timestamps: Whether to add created_at and updated_at timestamps
            
        Returns:
            InsertManyResult object
        """
        try:
            if add_timestamps:
                now = datetime.now()
                for doc in documents:
                    doc['created_at'] = now
                    doc['updated_at'] = now
            
            result = self.collection.insert_many(documents)
            logger.debug(f"Inserted {len(result.inserted_ids)} documents")
            return result
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in insert_many for {self.collection_name}: {e}")
            raise
    
    def find_one(self, filter_dict: Dict[str, Any] = None, projection: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Find a single document.
        
        Args:
            filter_dict: Query filter
            projection: Fields to include/exclude
            
        Returns:
            Document or None if not found
        """
        try:
            filter_dict = filter_dict or {}
            result = self.collection.find_one(filter_dict, projection)
            return result
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in find_one for {self.collection_name}: {e}")
            raise
    
    def find_many(self, filter_dict: Dict[str, Any] = None, projection: Dict[str, Any] = None, 
                  sort: List[tuple] = None, limit: int = None, skip: int = None) -> List[Dict[str, Any]]:
        """
        Find multiple documents.
        
        Args:
            filter_dict: Query filter
            projection: Fields to include/exclude
            sort: Sort specification
            limit: Maximum number of documents to return
            skip: Number of documents to skip
            
        Returns:
            List of documents
        """
        try:
            filter_dict = filter_dict or {}
            cursor = self.collection.find(filter_dict, projection)
            
            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)
            
            return list(cursor)
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in find_many for {self.collection_name}: {e}")
            raise
    
    def update_one(self, filter_dict: Dict[str, Any], update_dict: Dict[str, Any], 
                   upsert: bool = False, add_timestamp: bool = True) -> UpdateResult:
        """
        Update a single document.
        
        Args:
            filter_dict: Query filter
            update_dict: Update operations
            upsert: Whether to insert if document doesn't exist
            add_timestamp: Whether to add updated_at timestamp
            
        Returns:
            UpdateResult object
        """
        try:
            if add_timestamp:
                if '$set' not in update_dict:
                    update_dict['$set'] = {}
                update_dict['$set']['updated_at'] = datetime.now()
            
            result = self.collection.update_one(filter_dict, update_dict, upsert=upsert)
            logger.debug(f"Updated {result.modified_count} document(s)")
            return result
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in update_one for {self.collection_name}: {e}")
            raise
    
    def update_many(self, filter_dict: Dict[str, Any], update_dict: Dict[str, Any], 
                    add_timestamp: bool = True) -> UpdateResult:
        """
        Update multiple documents.
        
        Args:
            filter_dict: Query filter
            update_dict: Update operations
            add_timestamp: Whether to add updated_at timestamp
            
        Returns:
            UpdateResult object
        """
        try:
            if add_timestamp:
                if '$set' not in update_dict:
                    update_dict['$set'] = {}
                update_dict['$set']['updated_at'] = datetime.now()
            
            result = self.collection.update_many(filter_dict, update_dict)
            logger.debug(f"Updated {result.modified_count} document(s)")
            return result
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in update_many for {self.collection_name}: {e}")
            raise
    
    def delete_one(self, filter_dict: Dict[str, Any]) -> DeleteResult:
        """
        Delete a single document.
        
        Args:
            filter_dict: Query filter
            
        Returns:
            DeleteResult object
        """
        try:
            result = self.collection.delete_one(filter_dict)
            logger.debug(f"Deleted {result.deleted_count} document(s)")
            return result
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in delete_one for {self.collection_name}: {e}")
            raise
    
    def delete_many(self, filter_dict: Dict[str, Any]) -> DeleteResult:
        """
        Delete multiple documents.
        
        Args:
            filter_dict: Query filter
            
        Returns:
            DeleteResult object
        """
        try:
            result = self.collection.delete_many(filter_dict)
            logger.debug(f"Deleted {result.deleted_count} document(s)")
            return result
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in delete_many for {self.collection_name}: {e}")
            raise
    
    def count_documents(self, filter_dict: Dict[str, Any] = None) -> int:
        """
        Count documents matching filter.
        
        Args:
            filter_dict: Query filter
            
        Returns:
            Number of matching documents
        """
        try:
            filter_dict = filter_dict or {}
            return self.collection.count_documents(filter_dict)
            
        except PyMongoError as e:
            logger.error(f"MongoDB error in count_documents for {self.collection_name}: {e}")
            raise
    
    def find_by_id(self, object_id: Union[str, ObjectId]) -> Optional[Dict[str, Any]]:
        """
        Find document by ObjectId.
        
        Args:
            object_id: Document ObjectId
            
        Returns:
            Document or None if not found
        """
        if isinstance(object_id, str):
            object_id = ObjectId(object_id)
        
        return self.find_one({'_id': object_id})
    
    def create_index(self, keys: Union[str, List[tuple]], **kwargs) -> str:
        """
        Create an index on the collection.
        
        Args:
            keys: Index specification
            **kwargs: Additional index options
            
        Returns:
            Index name
        """
        try:
            return self.collection.create_index(keys, **kwargs)
        except PyMongoError as e:
            logger.error(f"MongoDB error creating index for {self.collection_name}: {e}")
            raise
