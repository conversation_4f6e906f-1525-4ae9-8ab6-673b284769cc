"""MongoDB connection management."""

import logging
from typing import Optional
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

from ..config import mongodb_config

logger = logging.getLogger(__name__)


class MongoDBConnection:
    """MongoDB connection manager with singleton pattern."""
    
    _instance: Optional['MongoDBConnection'] = None
    _client: Optional[MongoClient] = None
    _database: Optional[Database] = None
    
    def __new__(cls) -> 'MongoDBConnection':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
            self._connect()
    
    def _connect(self) -> None:
        """Establish connection to MongoDB."""
        try:
            self._client = MongoClient(
                mongodb_config.connection_string,
                maxPoolSize=mongodb_config.max_pool_size,
                minPoolSize=mongodb_config.min_pool_size,
                maxIdleTimeMS=mongodb_config.max_idle_time_ms,
                connectTimeoutMS=mongodb_config.connect_timeout_ms,
                serverSelectionTimeoutMS=mongodb_config.server_selection_timeout_ms,
                tz_aware=True,
                tzinfo=None  # Will use system timezone
            )
            
            # Test the connection
            self._client.admin.command('ping')
            self._database = self._client[mongodb_config.database]
            
            logger.info(f"Successfully connected to MongoDB: {mongodb_config.host}:{mongodb_config.port}")
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            raise
    
    @property
    def client(self) -> MongoClient:
        """Get MongoDB client."""
        if self._client is None:
            self._connect()
        return self._client
    
    @property
    def database(self) -> Database:
        """Get MongoDB database."""
        if self._database is None:
            self._connect()
        return self._database
    
    def close(self) -> None:
        """Close MongoDB connection."""
        if self._client:
            self._client.close()
            self._client = None
            self._database = None
            logger.info("MongoDB connection closed")
    
    def is_connected(self) -> bool:
        """Check if connected to MongoDB."""
        try:
            if self._client:
                self._client.admin.command('ping')
                return True
        except Exception:
            pass
        return False
    
    def __del__(self):
        """Cleanup on deletion."""
        self.close()


# Global connection instance
_connection = MongoDBConnection()


def get_database() -> Database:
    """Get the MongoDB database instance."""
    return _connection.database


def get_client() -> MongoClient:
    """Get the MongoDB client instance."""
    return _connection.client
